// src/components/Footer.jsx
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-bold mb-4 font-poppins">Dinkar Seeds Limited</h3>
            <p className="text-gray-300 mb-4">
              Providing quality seeds to farmers in Gujarat since 2002
            </p>
            <p className="text-gray-300">
              Sabarkantha, Gujarat, India
            </p>
          </div>
          
          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <FooterLink to="/">Home</FooterLink>
              <FooterLink to="/about">About Us</FooterLink>
              <FooterLink to="/products">Products</FooterLink>
              <FooterLink to="/contact">Contact</FooterLink>
            </ul>
          </div>
          
          {/* Products */}
          <div>
            <h3 className="text-lg font-bold mb-4">Our Products</h3>
            <ul className="space-y-2">
              <li className="text-gray-300">Wheat Seeds</li>
              <li className="text-gray-300">Cotton Seeds</li>
              <li className="text-gray-300">Vegetable Seeds</li>
              <li className="text-gray-300">Cereal Crops</li>
            </ul>
          </div>
          
          {/* Contact */}
          <div>
            <h3 className="text-lg font-bold mb-4">Contact Us</h3>
            <ul className="space-y-2 text-gray-300">
              <li>Phone: +91 9876543210</li>
              <li>Email: <EMAIL></li>
              <li>Address: Sabarkantha, Gujarat</li>
            </ul>
          </div>
        </div>
        
        {/* Copyright */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} Dinkar Seeds Limited. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

const FooterLink = ({ to, children }) => (
  <li>
    <Link to={to} className="text-gray-300 hover:text-green-400 transition">
      {children}
    </Link>
  </li>
);

export default Footer;