// src/pages/Home.jsx
import React, { useState, useEffect } from 'react';
import { FaSeedling, FaTractor, FaChartLine, FaUserFriends, FaLeaf, FaShippingFast, FaAward, FaPhone, FaEnvelope, FaMapMarkerAlt, FaArrowRight, FaPlay } from 'react-icons/fa';
import DinkarCompanyImage from '../assets/DinkarCompany.webp';

const Home = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState({});

  const heroSlides = [
    {
      title: "Premium Quality Seeds",
      subtitle: "for Bountiful Harvests",
      description: "Trusted by farmers in Gujarat for over 20 years. Premium quality seeds for maximum yield and profit.",
      cta: "Explore Products"
    },
    {
      title: "Scientific Innovation",
      subtitle: "meets Traditional Farming",
      description: "Research-driven seed varieties developed specifically for Gujarat's unique agricultural conditions.",
      cta: "Learn More"
    },
    {
      title: "Your Success",
      subtitle: "is Our Mission",
      description: "Expert guidance and support throughout your farming journey with guaranteed quality seeds.",
      cta: "Contact Experts"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const observeElement = (elementId) => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({ ...prev, [elementId]: true }));
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById(elementId);
    if (element) observer.observe(element);
    return () => observer.disconnect();
  };

  useEffect(() => {
    const cleanups = ['features', 'products', 'about', 'testimonials'].map(observeElement);
    return () => cleanups.forEach(cleanup => cleanup && cleanup());
  }, []);

  return (
    <div className="overflow-hidden">
      {/* Enhanced Hero Section */}
      <div className="relative min-h-screen bg-gradient-to-br from-green-800 via-green-700 to-emerald-600 text-white overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-green-400 rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-amber-400 rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-green-300 rounded-full opacity-10 animate-pulse"></div>
        </div>

        <div className="absolute inset-0 bg-black opacity-20"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10 min-h-screen flex items-center">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
            <div className="space-y-8">
              {/* Dynamic Content */}
              <div className="space-y-4">
                <div className="inline-flex items-center bg-amber-500/20 backdrop-blur-sm rounded-full px-4 py-2 text-amber-300 text-sm font-medium">
                  <FaAward className="mr-2" />
                  Gujarat's Most Trusted Seed Company
                </div>

                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold font-poppins leading-tight">
                  {heroSlides[currentSlide].title}
                  <br />
                  <span className="text-amber-400 bg-gradient-to-r from-amber-400 to-yellow-300 bg-clip-text text-transparent">
                    {heroSlides[currentSlide].subtitle}
                  </span>
                </h1>

                <p className="text-xl md:text-2xl text-green-100 max-w-2xl leading-relaxed">
                  {heroSlides[currentSlide].description}
                </p>
              </div>

              {/* Enhanced CTAs */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="group bg-amber-500 hover:bg-amber-600 text-white px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center justify-center">
                  {heroSlides[currentSlide].cta}
                  <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
                <button className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white px-8 py-4 rounded-xl text-lg font-bold border-2 border-white/30 transition-all duration-300 flex items-center justify-center">
                  <FaPlay className="mr-2" />
                  Watch Our Story
                </button>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">20+</div>
                  <div className="text-green-200 text-sm">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">10K+</div>
                  <div className="text-green-200 text-sm">Happy Farmers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">50+</div>
                  <div className="text-green-200 text-sm">Seed Varieties</div>
                </div>
              </div>
            </div>

            {/* Enhanced Visual Section */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                {/* Main Image Container */}
                <div className="relative w-96 h-96 rounded-3xl overflow-hidden shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                  <img
                    src={DinkarCompanyImage}
                    alt="Dinkar Seeds Company"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-green-900/50 to-transparent"></div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-6 -left-6 bg-amber-500 rounded-2xl p-4 shadow-xl animate-bounce">
                  <FaSeedling className="text-3xl text-white" />
                </div>

                <div className="absolute -bottom-6 -right-6 bg-white rounded-2xl p-6 shadow-xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-800">Gujarat's</div>
                    <div className="text-green-600 font-medium">#1 Choice</div>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-1/2 -right-8 w-16 h-16 bg-green-400 rounded-full opacity-20 animate-pulse"></div>
                <div className="absolute bottom-1/4 -left-8 w-12 h-12 bg-amber-400 rounded-full opacity-30 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide ? 'bg-amber-400 w-8' : 'bg-white/30'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Enhanced Features Section */}
      <div id="features" className="bg-gradient-to-br from-green-50 to-emerald-50 py-20 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className={`text-center mb-20 transition-all duration-1000 ${isVisible.features ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="inline-flex items-center bg-green-100 rounded-full px-6 py-2 text-green-700 text-sm font-medium mb-4">
              <FaAward className="mr-2" />
              Our Competitive Advantages
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-green-800 mb-6 font-poppins">
              Why Choose <span className="text-amber-600">Dinkar Seeds</span>
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-amber-500 to-amber-600 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the difference that two decades of agricultural expertise and innovation can make for your farm's success.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <EnhancedFeatureCard
              icon={<FaSeedling className="text-5xl" />}
              title="Premium Quality Seeds"
              description="Carefully selected and tested seeds for superior germination and yield with 95%+ success rate"
              color="green"
              delay="0"
            />
            <EnhancedFeatureCard
              icon={<FaTractor className="text-5xl" />}
              title="Farm-Tested Varieties"
              description="Varieties tested in local Gujarat conditions for optimal performance and climate adaptation"
              color="blue"
              delay="200"
            />
            <EnhancedFeatureCard
              icon={<FaChartLine className="text-5xl" />}
              title="High Yield Potential"
              description="Maximize your harvest with our scientifically developed seeds - up to 40% higher yields"
              color="amber"
              delay="400"
            />
            <EnhancedFeatureCard
              icon={<FaLeaf className="text-5xl" />}
              title="Disease Resistance"
              description="Seeds bred for resistance to common regional diseases, reducing crop loss by 60%"
              color="emerald"
              delay="600"
            />
            <EnhancedFeatureCard
              icon={<FaUserFriends className="text-5xl" />}
              title="Expert Farmer Support"
              description="24/7 expert guidance and support throughout the farming cycle with dedicated helpline"
              color="purple"
              delay="800"
            />
            <EnhancedFeatureCard
              icon={<FaShippingFast className="text-5xl" />}
              title="Guaranteed Timely Delivery"
              description="Reliable delivery to your farm before planting season with real-time tracking"
              color="orange"
              delay="1000"
            />
          </div>

          {/* Trust Indicators */}
          <div className="mt-20 bg-white rounded-3xl shadow-xl p-8 md:p-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-green-700">95%</div>
                <div className="text-gray-600 font-medium">Germination Rate</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-amber-600">40%</div>
                <div className="text-gray-600 font-medium">Higher Yields</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-blue-600">12</div>
                <div className="text-gray-600 font-medium">Districts Served</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-purple-600">24/7</div>
                <div className="text-gray-600 font-medium">Support Available</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Products Section */}
      <div id="products" className="py-20 bg-white relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-20 transition-all duration-1000 ${isVisible.products ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="inline-flex items-center bg-amber-100 rounded-full px-6 py-2 text-amber-700 text-sm font-medium mb-4">
              <FaSeedling className="mr-2" />
              Premium Seed Collection
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-green-800 mb-6 font-poppins">
              Our <span className="text-amber-600">Featured Products</span>
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-amber-500 to-amber-600 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our scientifically developed seed varieties, each crafted to thrive in Gujarat's unique climate and soil conditions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <EnhancedProductCard
              name="Premium Hybrid Wheat"
              category="Cereal Crops"
              price="₹2,500/bag"
              originalPrice="₹3,000"
              features={["40% higher yield", "Drought resistant", "Disease immunity", "90-day harvest"]}
              image="wheat"
              badge="Best Seller"
              badgeColor="green"
            />
            <EnhancedProductCard
              name="BT Cotton Elite"
              category="Cash Crops"
              price="₹4,200/bag"
              originalPrice="₹4,800"
              features={["Bollworm resistant", "Premium fiber quality", "High market value", "Extended picking"]}
              image="cotton"
              badge="Premium"
              badgeColor="purple"
            />
            <EnhancedProductCard
              name="Vegetable Master Pack"
              category="Vegetables"
              price="₹1,800/pack"
              originalPrice="₹2,200"
              features={["15 varieties included", "95% germination", "Organic certified", "Season-long harvest"]}
              image="vegetables"
              badge="Value Pack"
              badgeColor="blue"
            />
          </div>

          {/* Product Categories */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-8 md:p-12 mb-16">
            <h3 className="text-2xl md:text-3xl font-bold text-green-800 text-center mb-8">
              Complete Range of Agricultural Solutions
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <CategoryCard icon={<FaSeedling />} title="Cereal Crops" count="15+ Varieties" />
              <CategoryCard icon={<FaLeaf />} title="Cash Crops" count="12+ Varieties" />
              <CategoryCard icon={<FaTractor />} title="Vegetables" count="25+ Varieties" />
              <CategoryCard icon={<FaChartLine />} title="Pulses" count="8+ Varieties" />
            </div>
          </div>

          <div className="text-center">
            <button className="group bg-gradient-to-r from-green-700 to-green-800 hover:from-green-800 hover:to-green-900 text-white px-10 py-4 rounded-xl text-lg font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              View Complete Catalog
              <FaArrowRight className="ml-2 inline-block group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced About Section */}
      <div id="about" className="bg-gradient-to-br from-green-800 via-green-700 to-emerald-800 text-white py-20 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-10 w-40 h-40 bg-green-400 rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute bottom-20 left-10 w-32 h-32 bg-amber-400 rounded-full opacity-10 animate-bounce"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className={`transition-all duration-1000 ${isVisible.about ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>
              <div className="relative">
                <div className="relative w-full h-96 rounded-3xl overflow-hidden shadow-2xl">
                  <img
                    src={DinkarCompanyImage}
                    alt="Dinkar Seeds Company Building"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-green-900/70 to-transparent"></div>

                  {/* Overlay Content */}
                  <div className="absolute bottom-6 left-6 right-6">
                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">Since 2002</div>
                        <div className="text-green-200">Serving Gujarat's Farmers</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating Achievement Cards */}
                <div className="absolute -top-6 -right-6 bg-amber-500 rounded-2xl p-4 shadow-xl">
                  <div className="text-center">
                    <div className="text-xl font-bold">ISO</div>
                    <div className="text-sm">Certified</div>
                  </div>
                </div>

                <div className="absolute -bottom-6 -left-6 bg-white rounded-2xl p-4 shadow-xl text-green-800">
                  <div className="text-center">
                    <div className="text-xl font-bold">Award</div>
                    <div className="text-sm">Winner 2023</div>
                  </div>
                </div>
              </div>
            </div>

            <div className={`transition-all duration-1000 delay-300 ${isVisible.about ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>
              <div className="inline-flex items-center bg-amber-500/20 backdrop-blur-sm rounded-full px-6 py-2 text-amber-300 text-sm font-medium mb-6">
                <FaAward className="mr-2" />
                Gujarat's Agricultural Pioneer
              </div>

              <h2 className="text-4xl md:text-5xl font-bold mb-8 font-poppins leading-tight">
                About <span className="text-amber-400">Dinkar Seeds Limited</span>
              </h2>

              <div className="space-y-6 text-lg text-green-100 leading-relaxed">
                <p>
                  Established in 2002 in Himmatnagar, Sabarkantha, Dinkar Seeds has been at the forefront of agricultural innovation in Gujarat. We specialize in providing high-quality seeds tailored to the unique needs of Gujarat's farmers.
                </p>
                <p>
                  Our research-driven approach ensures that our products deliver superior yield and disease resistance, helping farmers maximize their profits while minimizing risks. With state-of-the-art facilities and a dedicated team of agricultural experts, we continue to set new standards in seed quality.
                </p>
              </div>

              {/* Enhanced Stats Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 mb-8">
                <EnhancedStatCard number="20+" label="Years Experience" icon={<FaAward />} />
                <EnhancedStatCard number="50+" label="Seed Varieties" icon={<FaSeedling />} />
                <EnhancedStatCard number="10K+" label="Farmers Served" icon={<FaUserFriends />} />
                <EnhancedStatCard number="12" label="Districts Covered" icon={<FaMapMarkerAlt />} />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="group bg-amber-500 hover:bg-amber-600 text-green-900 px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 transform hover:scale-105 flex items-center justify-center">
                  Learn More About Us
                  <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
                <button className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white px-8 py-4 rounded-xl text-lg font-bold border-2 border-white/30 transition-all duration-300 flex items-center justify-center">
                  <FaPhone className="mr-2" />
                  Contact Us
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Testimonials */}
      <div id="testimonials" className="py-20 bg-gradient-to-br from-green-50 to-emerald-50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className={`text-center mb-20 transition-all duration-1000 ${isVisible.testimonials ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="inline-flex items-center bg-green-100 rounded-full px-6 py-2 text-green-700 text-sm font-medium mb-4">
              <FaUserFriends className="mr-2" />
              Farmer Success Stories
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-green-800 mb-6 font-poppins">
              What <span className="text-amber-600">Farmers Say</span>
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-amber-500 to-amber-600 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Hear from farmers across Gujarat who have transformed their harvests and increased their profits with Dinkar Seeds.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <EnhancedTestimonialCard
              name="Rajesh Patel"
              location="Himmatnagar"
              occupation="Wheat Farmer"
              quote="Dinkar's wheat seeds increased my yield by 30% last season. The quality is unmatched in the region. Their support team guided me through the entire process."
              rating={5}
              image="farmer1"
              highlight="30% increase in yield"
            />
            <EnhancedTestimonialCard
              name="Meena Sharma"
              location="Idar"
              occupation="Cotton Farmer"
              quote="Their technical support helped me solve pest problems I was facing for years. The BT cotton seeds are amazing. Highly recommended for all farmers!"
              rating={5}
              image="farmer2"
              highlight="Pest-free farming"
            />
            <EnhancedTestimonialCard
              name="Vikram Singh"
              location="Modasa"
              occupation="Mixed Crop Farmer"
              quote="The germination rate of Dinkar seeds is excellent. I've been using them for 5 seasons now with consistent results. Best investment for my farm."
              rating={5}
              image="farmer3"
              highlight="5 seasons of success"
            />
          </div>

          {/* Trust Metrics */}
          <div className="bg-white rounded-3xl shadow-xl p-8 md:p-12">
            <div className="text-center mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-green-800 mb-4">
                Trusted by Thousands of Farmers
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Join the growing community of successful farmers who trust Dinkar Seeds for their agricultural needs.
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-green-700">4.9/5</div>
                <div className="text-gray-600 font-medium">Customer Rating</div>
                <div className="flex justify-center">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="h-5 w-5 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-amber-600">98%</div>
                <div className="text-gray-600 font-medium">Satisfaction Rate</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-blue-600">10,000+</div>
                <div className="text-gray-600 font-medium">Happy Farmers</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-purple-600">85%</div>
                <div className="text-gray-600 font-medium">Repeat Customers</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Contact Banner */}
      <div className="py-20 bg-gradient-to-br from-amber-500 via-amber-600 to-orange-600 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-orange-300 rounded-full opacity-10 animate-bounce"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full opacity-5 animate-pulse"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 text-amber-100 text-sm font-medium mb-6">
                <FaPhone className="mr-2" />
                Get Expert Consultation
              </div>

              <h2 className="text-4xl md:text-5xl font-bold mb-6 font-poppins leading-tight">
                Ready to Transform Your <span className="text-amber-200">Harvest?</span>
              </h2>

              <p className="text-xl mb-8 max-w-xl text-amber-100 leading-relaxed">
                Contact our agricultural experts today for personalized seed recommendations, farming solutions, and technical support tailored to your specific needs.
              </p>

              {/* Contact Info */}
              <div className="space-y-4 mb-8">
                <div className="flex items-center">
                  <FaPhone className="text-2xl mr-4 text-amber-200" />
                  <div>
                    <div className="font-bold text-lg">+91 98765 43210</div>
                    <div className="text-amber-200">24/7 Farmer Helpline</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <FaEnvelope className="text-2xl mr-4 text-amber-200" />
                  <div>
                    <div className="font-bold text-lg"><EMAIL></div>
                    <div className="text-amber-200">Quick Response Guaranteed</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-center lg:justify-end">
              <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 w-full max-w-md">
                <h3 className="text-2xl font-bold mb-6 text-center">Get Free Consultation</h3>
                <div className="space-y-4">
                  <button className="w-full group bg-green-800 hover:bg-green-900 text-white px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 transform hover:scale-105 flex items-center justify-center">
                    <FaPhone className="mr-2" />
                    Call Now: +91 98765 43210
                  </button>
                  <button className="w-full group bg-white hover:bg-gray-100 text-green-800 px-8 py-4 rounded-xl text-lg font-bold border-2 border-white transition-all duration-300 flex items-center justify-center">
                    <FaEnvelope className="mr-2" />
                    Send Message
                  </button>
                  <button className="w-full group bg-amber-700 hover:bg-amber-800 text-white px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 flex items-center justify-center">
                    <FaMapMarkerAlt className="mr-2" />
                    Visit Our Store
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description }) => (
  <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition duration-300 border border-green-100">
    <div className="text-green-700 mb-4">{icon}</div>
    <h3 className="text-xl font-bold text-green-800 mb-3">{title}</h3>
    <p className="text-gray-600">{description}</p>
  </div>
);

// Product Card Component
const ProductCard = ({ name, category, features }) => (
  <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-green-100 hover:shadow-xl transition duration-300">
    <div className="bg-gray-200 border-2 border-dashed w-full h-48"></div>
    <div className="p-6">
      <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-3">
        {category}
      </span>
      <h3 className="text-xl font-bold text-green-800 mb-3">{name}</h3>
      <ul className="space-y-2 mb-4">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center">
            <svg className="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            {feature}
          </li>
        ))}
      </ul>
      <button className="w-full bg-green-700 hover:bg-green-800 text-white py-2 rounded-lg font-medium transition">
        View Details
      </button>
    </div>
  </div>
);

// Stat Card Component
const StatCard = ({ number, label }) => (
  <div className="text-center bg-green-700/30 p-4 rounded-lg">
    <p className="text-3xl font-bold mb-1">{number}</p>
    <p className="text-sm font-medium">{label}</p>
  </div>
);

// Testimonial Card Component
const TestimonialCard = ({ name, location, quote }) => (
  <div className="bg-white p-8 rounded-xl shadow-md border border-green-100">
    <div className="flex items-center mb-6">
      <div className="bg-gray-200 border-2 border-dashed rounded-full w-16 h-16"></div>
      <div className="ml-4">
        <h4 className="font-bold text-green-800">{name}</h4>
        <p className="text-amber-600">{location}</p>
      </div>
    </div>
    <p className="text-gray-600 italic">"{quote}"</p>
    <div className="flex mt-4">
      {[...Array(5)].map((_, i) => (
        <svg key={i} className="h-5 w-5 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  </div>
);

export default Home;