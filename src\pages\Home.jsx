// src/pages/Home.jsx
import React from 'react';
import { FaS<PERSON>ling, FaTractor, FaChartLine, FaUserFriends, FaLeaf, FaShippingFast } from 'react-icons/fa';

const Home = () => {
  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-green-700 to-green-900 text-white">
        <div className="absolute inset-0 bg-black opacity-30"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-poppins leading-tight">
                Quality Seeds for <br />
                <span className="text-amber-400">Bountiful Harvests</span>
              </h1>
              <p className="text-xl mb-8 max-w-xl">
                <PERSON><PERSON> Seeds Limited - Trusted by farmers in Gujarat for over 20 years. Premium quality seeds for maximum yield and profit.
              </p>
              <div className="flex flex-wrap gap-4">
                <button className="bg-amber-500 hover:bg-amber-600 text-white px-8 py-3 rounded-lg text-lg font-bold transition transform hover:scale-105">
                  Explore Products
                </button>
                <button className="bg-white hover:bg-gray-100 text-green-800 px-8 py-3 rounded-lg text-lg font-bold border-2 border-white transition">
                  Contact Us
                </button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative">
                <div className="bg-gray-200 border-2 border-dashed rounded-full w-80 h-80 flex items-center justify-center">
                  <FaSeedling className="text-6xl text-green-800 opacity-50" />
                </div>
                <div className="absolute -bottom-5 -right-5 bg-amber-500 rounded-lg p-4 shadow-lg">
                  <div className="text-center">
                    <p className="font-bold text-2xl">20+</p>
                    <p className="font-medium">Years Experience</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-green-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-green-800 mb-4 font-poppins">
              Why Choose Dinkar Seeds
            </h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<FaSeedling className="text-4xl" />}
              title="Premium Quality Seeds"
              description="Carefully selected and tested seeds for superior germination and yield"
            />
            <FeatureCard 
              icon={<FaTractor className="text-4xl" />}
              title="Farm-Tested Varieties"
              description="Varieties tested in local conditions for optimal performance"
            />
            <FeatureCard 
              icon={<FaChartLine className="text-4xl" />}
              title="High Yield Potential"
              description="Maximize your harvest with our scientifically developed seeds"
            />
            <FeatureCard 
              icon={<FaLeaf className="text-4xl" />}
              title="Disease Resistance"
              description="Seeds bred for resistance to common regional diseases"
            />
            <FeatureCard 
              icon={<FaUserFriends className="text-4xl" />}
              title="Farmer Support"
              description="Expert guidance and support throughout the farming cycle"
            />
            <FeatureCard 
              icon={<FaShippingFast className="text-4xl" />}
              title="Timely Delivery"
              description="Reliable delivery to your farm before planting season"
            />
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-green-800 mb-4 font-poppins">
              Our Featured Products
            </h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Explore our premium seed varieties developed for Gujarat's unique agricultural conditions
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <ProductCard 
              name="Hybrid Wheat Seeds"
              category="Cereal Crops"
              features={["High yield potential", "Disease resistant", "Suitable for dry conditions"]}
            />
            <ProductCard 
              name="BT Cotton Seeds"
              category="Cash Crops"
              features={["Pest resistant", "Long fiber quality", "High market value"]}
            />
            <ProductCard 
              name="Vegetable Seeds Pack"
              category="Vegetables"
              features={["Assorted varieties", "High germination rate", "Disease resistant"]}
            />
          </div>
          
          <div className="text-center mt-12">
            <button className="bg-green-700 hover:bg-green-800 text-white px-8 py-3 rounded-lg text-lg font-bold transition transform hover:scale-105">
              View All Products
            </button>
          </div>
        </div>
      </div>

      {/* About Section */}
      <div className="bg-green-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center">
              <div className="bg-gray-200 border-2 border-dashed rounded-xl w-full h-96" />
            </div>
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-poppins">
                About Dinkar Seeds Limited
              </h2>
              <p className="text-lg mb-6">
                Established in 2002 in Himmatnagar, Sabarkantha, Dinkar Seeds has been at the forefront of agricultural innovation in Gujarat. We specialize in providing high-quality seeds tailored to the unique needs of Gujarat's farmers.
              </p>
              <p className="text-lg mb-8">
                Our research-driven approach ensures that our products deliver superior yield and disease resistance, helping farmers maximize their profits while minimizing risks.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <StatCard number="20+" label="Years Experience" />
                <StatCard number="50+" label="Seed Varieties" />
                <StatCard number="10K+" label="Farmers Served" />
                <StatCard number="12" label="Districts Covered" />
              </div>
              <button className="mt-8 bg-amber-500 hover:bg-amber-600 text-green-900 px-8 py-3 rounded-lg text-lg font-bold transition">
                Learn More About Us
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonials */}
      <div className="py-16 bg-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-green-800 mb-4 font-poppins">
              What Farmers Say
            </h2>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Hear from farmers who have transformed their harvests with Dinkar Seeds
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <TestimonialCard 
              name="Rajesh Patel"
              location="Himmatnagar"
              quote="Dinkar's wheat seeds increased my yield by 30% last season. The quality is unmatched in the region."
            />
            <TestimonialCard 
              name="Meena Sharma"
              location="Idar"
              quote="Their technical support helped me solve pest problems I was facing for years. Highly recommended!"
            />
            <TestimonialCard 
              name="Vikram Singh"
              location="Modasa"
              quote="The germination rate of Dinkar seeds is excellent. I've been using them for 5 seasons now with consistent results."
            />
          </div>
        </div>
      </div>

      {/* Contact Banner */}
      <div className="py-16 bg-gradient-to-r from-amber-500 to-amber-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-poppins">
                Ready to transform your harvest?
              </h2>
              <p className="text-xl mb-8 max-w-xl">
                Contact our agricultural experts today for personalized seed recommendations and farming solutions.
              </p>
            </div>
            <div className="flex justify-center">
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-green-800 hover:bg-green-900 text-white px-8 py-4 rounded-lg text-lg font-bold transition transform hover:scale-105">
                  Call Us: +91 98765 43210
                </button>
                <button className="bg-white hover:bg-gray-100 text-green-800 px-8 py-4 rounded-lg text-lg font-bold border-2 border-white transition">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description }) => (
  <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition duration-300 border border-green-100">
    <div className="text-green-700 mb-4">{icon}</div>
    <h3 className="text-xl font-bold text-green-800 mb-3">{title}</h3>
    <p className="text-gray-600">{description}</p>
  </div>
);

// Product Card Component
const ProductCard = ({ name, category, features }) => (
  <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-green-100 hover:shadow-xl transition duration-300">
    <div className="bg-gray-200 border-2 border-dashed w-full h-48"></div>
    <div className="p-6">
      <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-3">
        {category}
      </span>
      <h3 className="text-xl font-bold text-green-800 mb-3">{name}</h3>
      <ul className="space-y-2 mb-4">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center">
            <svg className="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            {feature}
          </li>
        ))}
      </ul>
      <button className="w-full bg-green-700 hover:bg-green-800 text-white py-2 rounded-lg font-medium transition">
        View Details
      </button>
    </div>
  </div>
);

// Stat Card Component
const StatCard = ({ number, label }) => (
  <div className="text-center bg-green-700/30 p-4 rounded-lg">
    <p className="text-3xl font-bold mb-1">{number}</p>
    <p className="text-sm font-medium">{label}</p>
  </div>
);

// Testimonial Card Component
const TestimonialCard = ({ name, location, quote }) => (
  <div className="bg-white p-8 rounded-xl shadow-md border border-green-100">
    <div className="flex items-center mb-6">
      <div className="bg-gray-200 border-2 border-dashed rounded-full w-16 h-16"></div>
      <div className="ml-4">
        <h4 className="font-bold text-green-800">{name}</h4>
        <p className="text-amber-600">{location}</p>
      </div>
    </div>
    <p className="text-gray-600 italic">"{quote}"</p>
    <div className="flex mt-4">
      {[...Array(5)].map((_, i) => (
        <svg key={i} className="h-5 w-5 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  </div>
);

export default Home;